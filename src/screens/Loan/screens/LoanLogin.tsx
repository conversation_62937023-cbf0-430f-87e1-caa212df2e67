import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';

import AssetifyLogo from '@/assets/logo/Logo.svg';
import MButton from '@/components/MButton';
import {PasswordInput} from '@/components/PasswordInput';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {LoanStackParamList} from '@/navigation/types';
import {AuthenticationInstance} from '@/services/BackendServices';
import {setAccessToken, setIsRegistered} from '@/storage/actions/loanActions';
import {Footer} from '@/styles/styled-components';
import {showWarningToast} from '@/utils/toast';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import theme from '@styles/theme';

type LoanNavigationProp = StackNavigationProp<LoanStackParamList>;

const LoanLogin: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation<LoanNavigationProp>();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const height = useBottomTabBarHeight();
  const offset = height + (theme.isSmallDevice ? 32 : 46);

  const handleSignIn = async () => {
    setLoading(true);

    try {
      const result = await AuthenticationInstance.post(`/user/signin`, {email, password});

      if (result.status === 200) {
        dispatch(setAccessToken(result.data.accessToken));
        dispatch(setIsRegistered(true));

        try {
          navigation.reset({
            index: 0,
            routes: [{name: 'LoanList'}],
          });
        } catch (loansError) {
          console.error('Error fetching loans:', loansError);
          navigation.replace('LoanConfirmation');
        }

        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      showWarningToast('Wrong email or password, please try again');
    }
  };

  return (
    <View style={styles.root}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <View style={styles.container}>
          <View style={styles.content}>
            <View style={styles.logoContainer}>
              <AssetifyLogo width={42} height={42} />
              <Text style={styles.logoText}>Sign In</Text>
            </View>

            <Text style={styles.title}>Sign in to your account</Text>

            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  placeholder="<EMAIL>"
                  placeholderTextColor={GlobalStyles.gray.gray700}
                  label="Email"
                />
              </View>

              <PasswordInput
                value={password}
                onChangeText={setPassword}
                onSubmitEditing={handleSignIn}
                label="Password"
                placeholder="••••••••"
                placeholderTextColor={GlobalStyles.gray.gray700}
                textContentType="none"
              />
            </View>

            <View style={styles.signUpContainer}>
              <Text style={styles.signUpText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('LoanRegister')}>
                <Text style={styles.signUpLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAwareScrollView>

      <KeyboardStickyView offset={{opened: offset}}>
        <Footer>
          <MButton
            text="Sign in"
            onPress={handleSignIn}
            isLoading={loading}
            disabled={!email || !password}
          />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default memo(LoanLogin);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: theme.layout.ph.screen,
    paddingTop: theme.layout.pv.lg * 3,
  },
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.lg,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  logoText: {
    fontSize: 22,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginLeft: theme.spacing.sm,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GlobalStyles.gray.gray900,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    gap: theme.spacing.md,
  },
  inputContainer: {
    width: '100%',
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: theme.spacing.lg,
  },
  signUpText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  signUpLink: {
    fontSize: 14,
    color: theme.colors.brand.main,
    fontWeight: '600',
    marginLeft: 4,
  },
  footer: {
    marginBottom: 12,
  },
});

import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import AssetifyLogo from '@/assets/logo/Logo.svg';
import MButton from '@/components/MButton';
import {PasswordInput} from '@/components/PasswordInput';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {LoanStackParamList} from '@/navigation/types';
import {AuthenticationInstance} from '@/services/BackendServices';
import {setIsRegistered} from '@/storage/actions/loanActions';
import {Footer} from '@/styles/styled-components';
import {showWarningToast} from '@/utils/toast';
import theme from '@styles/theme';

type LoanRegisterNavigationProp = StackNavigationProp<LoanStackParamList, 'LoanRegister'>;

const LoanRegister: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation<LoanRegisterNavigationProp>();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignUp = async () => {
    if (password !== confirmPassword) {
      showWarningToast('Password Mismatch');
      return;
    }
    setLoading(true);

    try {
      const result = await AuthenticationInstance.post(`/user/signup`, {email, password});

      if (result.status === 201 || result.status === 200) {
        // Assuming 201 for created or 200 for success
        setLoading(false);
        navigation.replace('LoanRegistrationSuccess');
        dispatch(setIsRegistered(true));
      }
    } catch (error: any) {
      setLoading(false);
      const errorMessage =
        error.response?.data?.message || 'Please check your details and try again.';
      showWarningToast('Registration Failed');
    }
  };

  return (
    <View style={styles.keyboardAvoidingView}>
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <AssetifyLogo width={42} height={42} />
            <Text style={styles.logoText}>Sign Up</Text>
          </View>

          <Text style={styles.title}>Create an account</Text>

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <TextInput
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                placeholder="<EMAIL>"
                placeholderTextColor={GlobalStyles.gray.gray700}
                label="Email"
              />
            </View>

            <PasswordInput
              value={password}
              onChangeText={setPassword}
              label="Password"
              placeholder="••••••••"
              placeholderTextColor={GlobalStyles.gray.gray700}
            />
            <PasswordInput
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              label="Confirm Password"
              placeholder="••••••••"
              placeholderTextColor={GlobalStyles.gray.gray700}
            />
          </View>
          <View style={styles.signInContainer}>
            <Text style={styles.signInText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('LoanLogin')}>
              <Text style={styles.signInLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <Footer>
        <MButton
          text="Create Account"
          onPress={handleSignUp}
          isLoading={loading}
          disabled={
            !email || !password || !confirmPassword || password !== confirmPassword
          }
        />
      </Footer>
    </View>
  );
};

export default memo(LoanRegister);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollViewContent: {
    paddingHorizontal: theme.layout.ph.screen * 2,
    paddingTop: theme.layout.pv.lg * 2,
  },
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen * 2,
    gap: theme.spacing.md,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  logoText: {
    fontSize: 22,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginLeft: theme.spacing.md,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GlobalStyles.gray.gray900,
    marginBottom: theme.spacing.lg,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    gap: theme.spacing.md,
  },
  inputContainer: {
    width: '100%',
  },
  signInContainer: {
    flexDirection: 'row',
    marginTop: theme.spacing.md,
  },
  signInText: {
    color: GlobalStyles.gray.gray800,
    fontSize: 14,
  },
  signInLink: {
    color: theme.colors.brand.main,
    fontWeight: '600',
    fontSize: 14,
  },
});

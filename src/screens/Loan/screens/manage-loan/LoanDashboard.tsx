import {RouteProp, useRoute} from '@react-navigation/native';
import React, {useCallback, useMemo, useState} from 'react';
import {ActivityIndicator, Alert, ScrollView, StyleSheet, Text, View} from 'react-native';
import PieChart from 'react-native-pie-chart';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {LoanStackParamList} from '@/navigation/types';
import {formatNumber} from '@/utils/index';
import {useUserLoans} from '../../helpers/loan-hooks';
import {Loan} from '../../helpers/loan-types';

type LoanDashboardRouteProp = RouteProp<LoanStackParamList, 'LoanDashboard'>;

const LoanDashboard = () => {
  const route = useRoute<LoanDashboardRouteProp>();
  const {loanId} = route.params;

  const [timeRange, setTimeRange] = useState('1W');

  const {data: loans, isLoading} = useUserLoans();

  // Demo data for doughnut chart
  const chartData = useMemo(() => {
    const rawData = [
      {
        value: 45000,
        color: '#3B82F6',
        label: 'Collateral Value',
        displayValue: '$45,000',
      },
      {value: 25000, color: '#10B981', label: 'Loan Amount', displayValue: '$25,000'},
    ];

    return {
      series: rawData.map((item) => ({value: item.value, color: item.color})),
      labels: rawData.map((item) => item.label),
      values: rawData.map((item) => item.displayValue),
      colors: rawData.map((item) => item.color),
    };
  }, []);

  const mockEthData = useMemo((): any[] => {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const basePrice = 3500;
    const volatility = 200;

    // Generate different data points based on selected time range
    const dataPoints: number =
      timeRange === '24H'
        ? 24
        : timeRange === '1W'
        ? 7 * 24
        : timeRange === '1M'
        ? 30 * 8
        : timeRange === '1Y'
        ? 365
        : 500;

    const timeInterval =
      timeRange === '24H'
        ? oneDayMs / 24
        : timeRange === '1W'
        ? oneDayMs
        : timeRange === '1M'
        ? oneDayMs
        : timeRange === '1Y'
        ? oneDayMs * 7
        : oneDayMs * 30;

    return Array.from({length: dataPoints}).map((_, i) => {
      const timestamp = now - (dataPoints - i - 1) * timeInterval;
      // Create some randomness but with a trend
      const randomFactor = Math.sin(i / (dataPoints / 10)) * 0.5 + Math.random() * 0.5;
      const price = basePrice + randomFactor * volatility;
      return {timestamp, price};
    });
  }, [timeRange]);

  // Calculate mock ETH stats
  const ethStats = useMemo(() => {
    if (mockEthData.length === 0) {
      return {
        currentPrice: 3500,
        priceChange: 50,
        priceChangePercentage: 1.45,
        highPrice: 3600,
        lowPrice: 3400,
      };
    }

    const lastDataPoint = mockEthData[mockEthData.length - 1];
    const earlierDataPoint = mockEthData[Math.max(mockEthData.length - 25, 0)];

    if (!lastDataPoint || !earlierDataPoint) {
      return {
        currentPrice: 3500,
        priceChange: 50,
        priceChangePercentage: 1.45,
        highPrice: 3600,
        lowPrice: 3400,
      };
    }

    const currentPrice = lastDataPoint.price;
    const yesterdayPrice = earlierDataPoint.price;
    const priceChange = currentPrice - yesterdayPrice;
    const priceChangePercentage = (priceChange / yesterdayPrice) * 100;
    const prices = mockEthData.map((point) => point.price);
    const highPrice = Math.max(...prices);
    const lowPrice = Math.min(...prices);

    return {
      currentPrice,
      priceChange,
      priceChangePercentage,
      highPrice,
      lowPrice,
    };
  }, [mockEthData]);

  const handleTimeRangeChange = (range: any) => {
    setTimeRange(range);
  };

  const loan = useMemo(() => {
    if (!loans) return null;
    return loans.find((l: Loan) => l._id === loanId);
  }, [loans, loanId]);

  const getRemainingTime = (createdAt: string, termMonths: number) => {
    const startDate = new Date(createdAt);
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + termMonths);

    const now = new Date();
    const remainingMs = endDate.getTime() - now.getTime();

    if (remainingMs <= 0) return 'Expired';

    const remainingDays = Math.floor(remainingMs / (1000 * 60 * 60 * 24));
    const remainingMonths = Math.floor(remainingDays / 30);

    if (remainingMonths > 0) {
      return `${remainingMonths} month${remainingMonths > 1 ? 's' : ''} ${
        remainingDays % 30
      } day${remainingDays % 30 !== 1 ? 's' : ''}`;
    }

    return `${remainingDays} day${remainingDays !== 1 ? 's' : ''}`;
  };

  const calculateCollateralValue = (loan: Loan) => {
    if (!loan) return 0;

    if (loan.terms.collateral.currency === 'ETH') {
      return loan.terms.collateral.amount;
    }

    if (loan.terms.collateral.currency === 'BTC') {
      return loan.terms.collateral.amount * 60000;
    }

    return 0;
  };

  const calculateCurrentLTV = (loan: Loan) => {
    if (!loan) return 0;

    const loanAmount = loan.terms.fiat.amount;
    const collateralValue = calculateCollateralValue(loan);

    if (collateralValue === 0) return 0;

    return (loanAmount / collateralValue) * 100;
  };

  const handleAddCollateral = useCallback(() => {
    Alert.alert('Add Collateral', 'This feature is not implemented yet.');
  }, []);

  const handleRepayLoan = useCallback(() => {
    Alert.alert('Repay Loan', 'This feature is not implemented yet.');
  }, []);

  const handleRequestSupport = useCallback(() => {
    Alert.alert('Request Support', 'This feature is not implemented yet.');
  }, []);

  if (isLoading || !loan) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
        <Text style={styles.loadingText}>Loading loan details...</Text>
      </View>
    );
  }

  const collateralValue = calculateCollateralValue(loan);
  const currentLTV = calculateCurrentLTV(loan);
  const isLTVHigh = currentLTV > 80;

  const nextPaymentDate = new Date();
  nextPaymentDate.setDate(nextPaymentDate.getDate() + 30);
  const formattedNextPaymentDate = nextPaymentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.chartContainer}>
        <View style={styles.chartWrapper}>
          <PieChart
            widthAndHeight={200}
            series={chartData.series}
            cover={{radius: 0.6, color: '#FFF'}}
          />
        </View>
      </View>

      <View style={styles.statusContainer}>
        <View style={[styles.statusIndicator, styles.statusActive]} />
        <Text style={styles.statusText}>Active Loan</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loan Summary</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Borrowed Amount</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.fiat.amount, {decimals: 2})}{' '}
            {loan.terms.fiat.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Interest Rate</Text>
          <Text style={styles.detailValue}>{loan.terms.interest}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Collateral</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.collateral.amount)} {loan.terms.collateral.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Loan Term</Text>
          <Text style={styles.detailValue}>{loan.terms.term} months</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Total Repayment Amount</Text>
          <Text style={styles.detailValue}>
            {loan.terms.fiat.amount.toLocaleString()} {loan.terms.fiat.currency}
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loan Activity</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Next Payment Due</Text>
          <Text style={styles.detailValue}>{formattedNextPaymentDate}</Text>
        </View>

        <View style={styles.emptyActivity}>
          <Text style={styles.emptyText}>No payment history available</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Collateral Monitoring</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Current Collateral Value</Text>
          <Text style={styles.detailValue}>
            {formatNumber(collateralValue, {decimals: 2})} {loan.terms.fiat.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Current LTV</Text>
          <Text style={[styles.detailValue, isLTVHigh && styles.warningText]}>
            {currentLTV.toFixed(2)}%
          </Text>
        </View>

        {isLTVHigh && (
          <View style={styles.warningContainer}>
            <Text style={styles.warningText}>
              LTV is too high; please consider depositing additional collateral to avoid
              liquidation.
            </Text>
          </View>
        )}
      </View>

      <View style={styles.actionContainer}>
        <MButton
          text="Add Collateral"
          onPress={handleAddCollateral}
          style={styles.actionButton}
          testID="add-collateral-button"
        />

        <MButton
          text="Repay Loan"
          onPress={handleRepayLoan}
          style={styles.actionButton}
          testID="repay-loan-button"
        />

        <MButton
          text="Request Support"
          onPress={handleRequestSupport}
          style={styles.actionButton}
          testID="request-support-button"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
  },
  chartContainer: {
    marginVertical: 42,
    paddingHorizontal: 16,
  },
  chartTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
    marginBottom: 24,
  },
  chartWrapper: {
    alignItems: 'center',
  },
  chartLegend: {
    marginTop: 24,
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  legendText: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  legendLabel: {
    fontSize: 14,
    color: GlobalStyles.gray.gray700,
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusActive: {
    backgroundColor: GlobalStyles.success.success500,
  },
  statusOverdue: {
    backgroundColor: '#F59E0B',
  },
  statusCompleted: {
    backgroundColor: GlobalStyles.gray.gray500,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  warningText: {
    color: '#F59E0B',
  },
  warningContainer: {
    backgroundColor: '#FEF3C7',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  emptyActivity: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  actionContainer: {
    width: '90%',
    alignSelf: 'center',
    marginBottom: 32,
  },
  actionButton: {
    marginBottom: 12,
    height: 48,
  },
});

export default LoanDashboard;

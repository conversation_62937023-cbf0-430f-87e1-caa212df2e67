import LoadingHandler from '@/components/LoadingHandler';
import MButton from '@/components/MButton';
import {CONSTANTS} from '@/screens/LightningNetwork/helpers/lightningConstants';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import {VerificationCodeInput} from './VerificationCodeInput';

const TwoFactorAuthScreen: React.FC = () => {
  const [qrCodeData, setQrCodeData] = useState<any>();
  const [code, setCode] = useState('');

  const height = useBottomTabBarHeight();
  const offset = height + (theme.isSmallDevice ? 32 : 46);

  console.log(height);

  const onSubmit = () => {};

  const fetchQrCode = async () => {
    const response = await fetch(CONSTANTS.QR_CODE_URL, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: '<EMAIL>',
      }),
    });

    return response.json();
  };

  useEffect(() => {
    fetchQrCode().then((data) => setQrCodeData(data));
  }, []);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <Text style={styles.descText}>Set up two-factor authentication.</Text>
        <Text style={styles.descSubText}>
          To be able to authorize transactions you need to scan this QR Code with your
          Google Authentication App and enter the verification code below.
        </Text>

        <View>
          {!qrCodeData ? (
            <View style={styles.qr}>
              <LoadingHandler />
            </View>
          ) : (
            <TouchableOpacity>
              <Image style={styles.qr} source={{uri: qrCodeData?.data}} />
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.verificationLabel}>Verification Code</Text>
        <VerificationCodeInput value={code} onChange={setCode} length={6} />
      </KeyboardAwareScrollView>

      <KeyboardStickyView
        offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
      >
        <Footer>
          <MButton text="Next" disabled={code.length !== 6} onPress={onSubmit} />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default TwoFactorAuthScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 42,
  },
  contentContainer: {
    flexGrow: 1,
  },
  descText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  descSubText: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 24,
  },
  verificationLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  qr: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  stickyFooter: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});
